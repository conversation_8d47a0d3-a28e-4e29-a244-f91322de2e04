import factory
from factory.django import DjangoModelFactory
from django.db.models import signals

from app.models import LiveStream, LiveStreamEvent
from tests.factories import AssetFactory, OrganizationFactory


class LiveStreamFactory(DjangoModelFactory):
    class Meta:
        model = LiveStream

    rtmp_url = factory.Faker("url", schemes=["rtmp"])
    stream_key = factory.Faker("uuid4")
    hls_url_path = factory.LazyAttribute(lambda o: f"/live/{o.stream_key}/index.m3u8")
    status = LiveStream.Status.NOT_STARTED
    server_status = LiveStream.ServerStatus.NOT_CREATED
    organization = factory.SubFactory(OrganizationFactory)
    
    @factory.post_generation
    def asset(self, create, extracted, **kwargs):
        if not create:
            return
        
        if extracted:
            self.asset = extracted
        else:
            with factory.django.mute_signals(signals.post_save):
                self.asset = AssetFactory(organization=self.organization)
                self.save()
    
    enable_drm = False
    transcode_recorded_video = True
    store_recorded_video = True
    resolutions = []


class LiveStreamEventFactory(DjangoModelFactory):
    class Meta:
        model = LiveStreamEvent

    type = LiveStreamEvent.Type.CREATED
    live_stream = factory.SubFactory(LiveStreamFactory)
    organization = factory.LazyAttribute(lambda o: o.live_stream.organization)
    data = None
