from datetime import timedelta
from unittest.mock import patch
import factory

from django.test import TestCase, override_settings
from django.utils import timezone
from django.db.models import signals

from app.models import LiveStream, LiveStreamEvent
from app.domain.live_stream import get_streamed_duration
from tests.factories import LiveStreamEventFactory
from tests.mixins import OrganizationMixin, AssetMixin


@override_settings(DEBUG=True)
@patch('app.domain.cloudfront.create_cdn', return_value=('test-cdn-id', 'https://test-cdn.example.com'))
@patch('app.domain.cloud_storage.create_bucket', return_value=None)
@patch('app.domain.cloudfront.create_key_group', return_value='test-key-group-id')
class LiveStreamDurationTestCase(OrganizationMixin, AssetMixin, TestCase):
    @factory.django.mute_signals(signals.post_save)
    def setUp(self, *mocks):
        self.organization = self.create_organization()
        self.asset = self.create_asset(organization=self.organization)

        self.live_stream = self.create_livestream(
            asset=self.asset,
            rtmp_url="rtmp://test.com/live",
            stream_key="test-stream-key",
            hls_url_path="/live/test-stream-key/index.m3u8"
        )
        
    def test_get_streamed_duration_with_no_events(self, *mocks):
        self.assertEqual(get_streamed_duration(self.live_stream), 0)
        
    def test_get_streamed_duration_with_events(self, *mocks):
        now = timezone.now()
        publish_time = now - timedelta(minutes=10)

        publish_event = LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH,
            created=publish_time
        )

        unpublish_time = now - timedelta(minutes=5)
        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH_DONE,
            created=unpublish_time
        )

        self.assertEqual(get_streamed_duration(self.live_stream), 300)
        
    def test_get_streamed_duration_with_multiple_sessions(self, *mocks):
        now = timezone.now()

        publish1 = LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH,
            created=now - timedelta(minutes=20)
        )
        
        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH_DONE,
            created=now - timedelta(minutes=15)
        )

        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH,
            created=now - timedelta(minutes=10)
        )
        
        LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH_DONE,
            created=now - timedelta(minutes=5)
        )

        self.assertEqual(get_streamed_duration(self.live_stream), 600)
        
    def test_active_stream_duration(self, *mocks):
        self.live_stream.status = LiveStream.Status.STREAMING
        self.live_stream.save()

        now = timezone.now()
        publish_time = now - timedelta(minutes=10)
        
        publish_event = LiveStreamEventFactory(
            organization=self.organization,
            live_stream=self.live_stream,
            type=LiveStreamEvent.Type.ON_PUBISH,
            created=publish_time
        )

        self.assertAlmostEqual(get_streamed_duration(self.live_stream), 600, delta=5)
