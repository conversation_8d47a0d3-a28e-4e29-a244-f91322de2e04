from unittest.mock import patch

from django_multitenant.utils import set_current_tenant

from app.domain.video_trim.exceptions import ManifestHandlingError
from app.domain.video_trim.manifest_domain.dash import TrimContext as DashTrimContext
from app.domain.video_trim.manifest_domain.dash import trim_dash_manifests
from app.domain.video_trim.manifest_domain.hls import TrimContext, trim_hls_manifests
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin


class TestTrimContext(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        self.organization.cdn_url = "https://cdn.example.com/"
        set_current_tenant(self.organization)
        self.video = self.create_video()

    def test_trim_context_initialization(self):
        context = TrimContext(self.video, 300, 1800)

        self.assertEqual(context.video, self.video)
        self.assertEqual(context.start_time, 300)
        self.assertEqual(context.end_time, 1800)
        self.assertIsNotNone(context.trim_id)
        self.assertEqual(context.base_path, f"transcoded/{self.video.asset.uuid}")
        self.assertEqual(
            context.master_url,
            f"https://cdn.example.com/transcoded/{self.video.asset.uuid}/video.m3u8",
        )
        self.assertEqual(
            context.master_path,
            f"transcoded/{self.video.asset.uuid}/trimmed_{context.trim_id}.m3u8",
        )


class TestTrimHLSManifests(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        self.organization.cdn_url = "https://cdn.example.com/"
        self.organization.bucket_name = "test-bucket"
        set_current_tenant(self.organization)
        self.video = self.create_video()

    @patch("app.domain.video_trim.manifest_domain.hls.get_m3u8_content")
    @patch("app.domain.video_trim.manifest_domain.hls.upload_manifest_content")
    @patch("app.domain.video_trim.manifest_domain.hls.trim_playlist_from_url")
    def test_trim_hls_manifests_multi_variant(
        self, mock_trim_playlist, mock_upload, mock_get_content
    ):
        master_content = """#EXTM3U
            #EXT-X-VERSION:3
            #EXT-X-STREAM-INF:BANDWIDTH=1000000,RESOLUTION=1280x720
            720p_h264/playlist.m3u8
            #EXT-X-STREAM-INF:BANDWIDTH=500000,RESOLUTION=854x480
            480p_h264/playlist.m3u8"""

        mock_get_content.return_value = master_content
        mock_trim_playlist.return_value = "#EXTM3U\n#EXT-X-VERSION:3\n#EXT-X-ENDLIST"

        result = trim_hls_manifests(self.video, 300, 1800)

        self.assertIn("master", result)
        self.assertIn("trim_id", result)
        self.assertTrue(result["master"].startswith("transcoded/"))
        self.assertTrue(result["master"].endswith(".m3u8"))

        self.assertEqual(mock_trim_playlist.call_count, 2)
        self.assertEqual(mock_upload.call_count, 3)

    @patch("app.domain.video_trim.manifest_domain.hls.get_m3u8_content")
    @patch("app.domain.video_trim.manifest_domain.hls.upload_manifest_content")
    @patch("app.domain.video_trim.manifest_domain.hls.trim_playlist_from_url")
    @patch("app.domain.video_trim.manifest_domain.hls.create_simple_master_playlist")
    def test_trim_hls_manifests_single_variant(
        self, mock_create_master, mock_trim_playlist, mock_upload, mock_get_content
    ):
        single_variant_content = """#EXTM3U
                #EXT-X-VERSION:3
                #EXT-X-TARGETDURATION:10
                #EXTINF:10.0,
                segment1.ts
                #EXTINF:10.0,
                segment2.ts
                #EXT-X-ENDLIST"""

        mock_get_content.return_value = single_variant_content
        mock_trim_playlist.return_value = "#EXTM3U\n#EXT-X-VERSION:3\n#EXT-X-ENDLIST"
        mock_create_master.return_value = (
            "#EXTM3U\n#EXT-X-VERSION:3\ndefault_h264/trimmed_test.m3u8"
        )

        result = trim_hls_manifests(self.video, 300, 1800)

        self.assertIn("master", result)
        self.assertIn("trim_id", result)
        mock_create_master.assert_called_once()
        self.assertEqual(mock_upload.call_count, 2)

    @patch("app.domain.video_trim.manifest_domain.hls.get_m3u8_content")
    def test_trim_hls_manifests_fetch_failure(self, mock_get_content):
        mock_get_content.return_value = None

        with self.assertRaises(ManifestHandlingError) as context:
            trim_hls_manifests(self.video, 300, 1800)

        self.assertIn("Could not fetch master playlist", str(context.exception))

    @patch("app.domain.video_trim.manifest_domain.hls.get_m3u8_content")
    def test_trim_hls_manifests_invalid_playlist(self, mock_get_content):
        invalid_content = "#EXTM3U\n#EXT-X-VERSION:3"
        mock_get_content.return_value = invalid_content

        with self.assertRaises(ManifestHandlingError) as context:
            trim_hls_manifests(self.video, 300, 1800)

        self.assertIn("Invalid HLS playlist", str(context.exception))


class TestDashTrimContext(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        self.organization.cdn_url = "https://cdn.example.com/"
        set_current_tenant(self.organization)
        self.video = self.create_video()

    def test_dash_trim_context_initialization(self):
        context = DashTrimContext(self.video, 300, 1800)

        self.assertEqual(context.video, self.video)
        self.assertEqual(context.start_time, 300)
        self.assertEqual(context.end_time, 1800)
        self.assertIsNotNone(context.trim_id)
        self.assertEqual(context.base_path, f"transcoded/{self.video.asset.uuid}")
        self.assertEqual(
            context.master_url,
            f"https://cdn.example.com/transcoded/{self.video.asset.uuid}/video.mpd",
        )
        self.assertEqual(
            context.master_path,
            f"transcoded/{self.video.asset.uuid}/trimmed_{context.trim_id}.mpd",
        )


class TestTrimDashManifests(TestCase, OrganizationMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        self.organization.cdn_url = "https://cdn.example.com/"
        self.organization.bucket_name = "test-bucket"
        set_current_tenant(self.organization)
        self.video = self.create_video()

    @patch("app.domain.video_trim.manifest_domain.dash.get_manifest_content")
    @patch("app.domain.video_trim.manifest_domain.dash.upload_manifest_content")
    @patch("app.domain.video_trim.manifest_domain.dash.trim_mpd_content")
    def test_trim_dash_manifests_success(
        self, mock_trim_mpd, mock_upload, mock_get_content
    ):
        mpd_content = """<?xml version="1.0" encoding="UTF-8"?>
        <MPD xmlns="urn:mpeg:dash:schema:mpd:2011" mediaPresentationDuration="PT30M">
        </MPD>"""

        trimmed_content = """<?xml version="1.0" encoding="UTF-8"?>
        <MPD xmlns="urn:mpeg:dash:schema:mpd:2011" mediaPresentationDuration="PT25M">
        </MPD>"""

        mock_get_content.return_value = mpd_content
        mock_trim_mpd.return_value = trimmed_content

        result = trim_dash_manifests(self.video, 300, 1800)

        self.assertIn("master", result)
        self.assertIn("trim_id", result)
        self.assertTrue(result["master"].startswith("transcoded/"))
        self.assertTrue(result["master"].endswith(".mpd"))

        mock_get_content.assert_called_once()
        mock_trim_mpd.assert_called_once_with(mpd_content, 300, 1800)
        mock_upload.assert_called_once()

    @patch("app.domain.video_trim.manifest_domain.dash.get_manifest_content")
    def test_trim_dash_manifests_fetch_failure(self, mock_get_content):
        mock_get_content.return_value = None

        with self.assertRaises(ManifestHandlingError) as context:
            trim_dash_manifests(self.video, 300, 1800)

        self.assertIn("Could not fetch master MPD", str(context.exception))
