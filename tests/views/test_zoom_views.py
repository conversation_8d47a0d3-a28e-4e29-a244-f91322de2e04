import json
from unittest.mock import patch
from urllib.parse import parse_qs, urlencode, urlparse

import requests
from django.http import Http404
from django.test import override_settings
from django.urls import reverse
from django_multitenant.utils import set_current_tenant

from app.domain.zoom import ZoomError
from app.models.asset import Asset
from app.models.zoom import (
    EventType,
    ImportStatus,
    WebhookStatus,
    ZoomAccount,
    ZoomRecording,
    ZoomWebhookLog,
)
from app.views.zoom import (
    ZoomAccountUpdateView,
    ZoomAuthorizeView,
    ZoomDisconnectView,
    ZoomOAuthCallbackView,
    ZoomRecordingWebhookView,
)
from tests import TestCase
from tests.mixins import AssetMixin, OrganizationMixin, ZoomMixin


class TestZoomAuthorizeView(OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.organization.created_by
        set_current_tenant(self.organization)

    @override_settings(
        ZOOM_CLIENT_ID="test_client_id",
        SITE_URL="https://example.com",
    )
    def test_authorize_redirects_to_zoom_with_correct_params(self):
        request = self.get_request("/settings/integrations/", user=self.user)

        response = ZoomAuthorizeView.as_view()(request)

        self.assertEqual(response.status_code, 302)
        redirect_url = response.url
        parsed_url = urlparse(redirect_url)
        query_params = parse_qs(parsed_url.query)

        self.assertEqual(query_params["response_type"], ["code"])
        self.assertEqual(query_params["client_id"], ["test_client_id"])
        self.assertEqual(
            query_params["redirect_uri"],
            ["https://example.com/settings/integrations/zoom/callback/"],
        )
        self.assertIn("state", query_params)
        self.assertTrue(request.session.get("zoom_oauth_state"))


class TestZoomOAuthCallbackView(OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.organization.created_by
        set_current_tenant(self.organization)
        self.url = reverse("zoom_oauth_callback")
        self.state = "test_state"

    @patch("app.views.messages.error")
    def test_view_should_raise_zoom_oauth_error_if_state_token_is_invalid(
        self, mock_error
    ):
        request = self.get_request(f"{self.url}?state=invalid_state", user=self.user)
        request.session = {"zoom_oauth_state": self.state}

        response = ZoomOAuthCallbackView.as_view()(request)

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("integrations_settings"))
        self.assertNotIn("zoom_oauth_state", request.session)
        mock_error.assert_called_once_with(
            request, "Failed to authenticate with Zoom. Please try again."
        )

    @patch("app.views.messages.success")
    @patch("app.views.zoom.fetch_zoom_tokens_using_authorization_code")
    @patch("app.views.zoom.fetch_zoom_user_info")
    @patch("app.views.zoom.store_zoom_account_in_db")
    def test_view_should_handle_successful_oauth_flow(
        self, mock_save_account, mock_fetch_info, mock_exchange, mock_success
    ):
        request = self.get_request(
            f"{self.url}?state={self.state}&code=test_code", user=self.user
        )
        request.session = {"zoom_oauth_state": self.state}

        mock_tokens = {"access_token": "test_token"}
        mock_user_info = {"id": "test_id"}
        mock_exchange.return_value = mock_tokens
        mock_fetch_info.return_value = mock_user_info

        response = ZoomOAuthCallbackView.as_view()(request)

        self.assertEqual(response.status_code, 302)
        self.assertEqual(
            response.url, reverse("integrations_settings") + "?zoom_connected=true"
        )
        mock_exchange.assert_called_once_with("test_code")
        mock_fetch_info.assert_called_once_with("test_token")
        mock_save_account.assert_called_once_with(
            self.user, mock_user_info, mock_tokens
        )

    @patch("app.views.messages.error")
    def test_view_should_raise_zoom_oauth_error_if_code_is_missing(self, mock_error):
        request = self.get_request(f"{self.url}?state={self.state}", user=self.user)
        request.session = {"zoom_oauth_state": self.state}

        response = ZoomOAuthCallbackView.as_view()(request)

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("integrations_settings"))
        mock_error.assert_called_once_with(
            request, "Failed to authenticate with Zoom. Please try again."
        )

    @patch("app.views.messages.error")
    @patch(
        "app.views.zoom.fetch_zoom_tokens_using_authorization_code",
        side_effect=requests.exceptions.RequestException("Zoom down"),
    )
    def test_view_should_raise_request_exception(self, mock_exchange, mock_error):
        request = self.get_request(
            f"{self.url}?state={self.state}&code=test_code", user=self.user
        )
        request.session = {"zoom_oauth_state": self.state}

        response = ZoomOAuthCallbackView.as_view()(request)

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("integrations_settings"))
        mock_error.assert_called_once_with(
            request,
            "Failed to connect to Zoom. Please check your internet connection and try again.",
        )


class TestZoomDisconnectView(OrganizationMixin, TestCase):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.organization.created_by
        self.user.current_organization = self.organization
        set_current_tenant(self.organization)
        self.url = reverse("zoom_disconnect")
        self.zoom_account = ZoomAccount.objects.create(
            organization=self.organization,
            user=self.user,
            zoom_user_id="test_id",
            email="<EMAIL>",
            access_token="test_token",
            refresh_token="test_refresh",
            expires_at="2024-03-20 00:00:00+00:00",
        )

    @patch("app.views.zoom.revoke_zoom_account")
    @patch("app.views.messages.success")
    def test_view_should_revoke_zoom_account(self, mock_success, mock_revoke):
        request = self.get_request(self.url, user=self.user, method="POST")
        response = ZoomDisconnectView.as_view()(request)

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("integrations_settings"))
        mock_revoke.assert_called_once_with(self.zoom_account)

    @patch("app.views.zoom.revoke_zoom_account")
    @patch("app.views.messages.error")
    def test_view_should_raise_zoom_error(self, mock_error, mock_revoke):
        request = self.get_request(self.url, user=self.user, method="POST")
        mock_revoke.side_effect = ZoomError(
            "Failed to disconnect from Zoom. Please try again."
        )

        response = ZoomDisconnectView.as_view()(request)

        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("integrations_settings"))
        mock_error.assert_called_once_with(
            request, "Failed to disconnect from Zoom. Please try again."
        )

    @patch("app.views.messages.error")
    def test_view_should_raise_http_404_if_account_does_not_exist(self, mock_error):
        self.zoom_account.delete()
        request = self.get_request(self.url, user=self.user, method="POST")

        with self.assertRaises(Http404):
            ZoomDisconnectView.as_view()(request)


class TestZoomRecordingWebhookView(OrganizationMixin, TestCase, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.organization.created_by
        set_current_tenant(self.organization)
        self.url = reverse("zoom_recording_webhook")
        self.valid_payload = {
            "event": "recording.started",
            "payload": {
                "account_id": "test_account",
                "object": {"recording_files": []},
            },
        }
        self.zoom_account = ZoomAccount.objects.create(
            organization=self.organization,
            user=self.user,
            zoom_user_id="test_host_id",
            access_token="test_token",
            refresh_token="test_refresh",
        )

    @patch("app.views.zoom.is_valid_zoom_signature")
    @patch("app.views.zoom.store_zoom_recording_webhook_event")
    def test_view_should_return_200_if_webhook_request_is_valid(
        self, mock_record_event, mock_validate_signature
    ):
        mock_validate_signature.return_value = True

        request = self.get_request(
            self.url,
            user=self.user,
            method="POST",
            data=json.dumps(self.valid_payload),
            content_type="application/json",
        )

        response = ZoomRecordingWebhookView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), "OK")
        mock_record_event.assert_called_once_with(self.valid_payload)

    @patch("app.views.zoom.is_valid_zoom_signature")
    @patch("app.views.zoom.store_zoom_recording_webhook_event")
    def test_view_should_return_400_if_signature_is_invalid(
        self, mock_record_event, mock_validate_signature
    ):
        mock_validate_signature.return_value = False

        request = self.get_request(
            self.url,
            user=self.user,
            method="POST",
            data=json.dumps(self.valid_payload),
            content_type="application/json",
        )

        response = ZoomRecordingWebhookView.as_view()(request)

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.content.decode(), "Invalid signature")
        mock_record_event.assert_not_called()

    def test_view_should_return_400_if_json_payload_is_invalid(self):
        request = self.get_request(
            self.url,
            user=self.user,
            method="POST",
            data="invalid json",
            content_type="application/json",
        )

        response = ZoomRecordingWebhookView.as_view()(request)

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.content.decode(), "Invalid JSON")

    def test_view_should_return_405_if_method_is_not_post(self):
        request = self.get_request(self.url, user=self.user, method="GET")

        response = ZoomRecordingWebhookView.as_view()(request)
        self.assertEqual(response.status_code, 405)

    @patch("app.views.zoom.is_valid_zoom_signature")
    def test_should_store_recording_completed_event(self, mock_store_event):
        payload = {
            "event": "recording.completed",
            "payload": {
                "object": {
                    "host_id": "test_host_id",
                    "uuid": "test_meeting_uuid",
                }
            },
        }

        request = self.get_request(
            self.url,
            user=self.user,
            method="POST",
            data=json.dumps(payload),
            content_type="application/json",
        )

        response = ZoomRecordingWebhookView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        webhook_log = ZoomWebhookLog.objects.last()
        self.assertIsNotNone(webhook_log)
        self.assertEqual(webhook_log.event_type, EventType.RECORDING_COMPLETED)
        self.assertEqual(webhook_log.organization, self.organization)
        self.assertEqual(webhook_log.status, WebhookStatus.PENDING)
        self.assertEqual(webhook_log.payload, payload)

    @patch("app.views.zoom.is_valid_zoom_signature")
    def test_should_store_recording_started_event(self, mock_validate_signature):
        payload = {
            "event": "recording.started",
            "payload": {
                "object": {
                    "host_id": "test_host_id",
                }
            },
        }

        request = self.get_request(
            self.url,
            user=self.user,
            method="POST",
            data=json.dumps(payload),
            content_type="application/json",
        )

        response = ZoomRecordingWebhookView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        webhook_log = ZoomWebhookLog.objects.last()
        self.assertIsNotNone(webhook_log)
        self.assertEqual(webhook_log.event_type, EventType.RECORDING_STARTED)
        self.assertEqual(webhook_log.status, WebhookStatus.SUCCESS)

    @patch("app.views.zoom.is_valid_zoom_signature")
    def test_should_not_store_event_with_invalid_host_id(self, mock_validate_signature):
        payload = {
            "event": "recording.completed",
            "payload": {
                "object": {
                    "host_id": "invalid_host_id",
                }
            },
        }

        request = self.get_request(
            self.url,
            user=self.user,
            method="POST",
            data=json.dumps(payload),
            content_type="application/json",
        )

        response = ZoomRecordingWebhookView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(ZoomWebhookLog.objects.count(), 0)

    @patch("app.views.zoom.is_valid_zoom_signature")
    def test_should_not_store_event_with_invalid_event_type(
        self, mock_validate_signature
    ):
        payload = {
            "event": "invalid.event",
            "payload": {
                "object": {
                    "host_id": "test_host_id",
                }
            },
        }

        request = self.get_request(
            self.url,
            user=self.user,
            method="POST",
            data=json.dumps(payload),
            content_type="application/json",
        )

        response = ZoomRecordingWebhookView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(ZoomWebhookLog.objects.count(), 0)

    @patch("app.views.zoom.is_valid_zoom_signature")
    def test_should_mark_duplicate_recording_completed_event_as_processed(
        self, mock_validate_signature
    ):
        meeting_uuid = "test_meeting_uuid"
        asset = self.create_asset(type=Asset.Type.VIDEO)
        ZoomRecording.objects.create(
            asset=asset,
            organization=self.organization,
            meeting_uuid=meeting_uuid,
            status=ImportStatus.IMPORTED,
            zoom_user_id="test_host_id",
            recording_uuid="test_recording_uuid",
        )
        payload = {
            "event": "recording.completed",
            "payload": {
                "object": {
                    "host_id": "test_host_id",
                    "uuid": meeting_uuid,
                }
            },
        }

        request = self.get_request(
            self.url,
            user=self.user,
            method="POST",
            data=json.dumps(payload),
            content_type="application/json",
        )

        response = ZoomRecordingWebhookView.as_view()(request)

        self.assertEqual(response.status_code, 200)
        webhook_log = ZoomWebhookLog.objects.last()
        self.assertIsNotNone(webhook_log)
        self.assertEqual(webhook_log.event_type, EventType.RECORDING_COMPLETED)
        self.assertEqual(webhook_log.status, WebhookStatus.SKIPPED)


class TestZoomAccountUpdateView(OrganizationMixin, TestCase, ZoomMixin, AssetMixin):
    def setUp(self):
        self.organization = self.create_organization()
        self.user = self.organization.created_by
        self.user.current_organization = self.organization
        set_current_tenant(self.organization)
        self.url = reverse("zoom_account_update")
        self.zoom_account = self.create_zoom_account()

    @patch("app.views.messages.success")
    def test_view_should_return_user_zoom_account_correctly(self, mock_success):
        self.zoom_account.user = self.user
        self.zoom_account.organization = self.organization
        self.zoom_account.save()
        request = self.get_request(self.url, user=self.user, method="POST")
        view = ZoomAccountUpdateView()
        view.request = request

        self.assertEqual(view.get_object(), self.zoom_account)

    def test_view_should_raise_404_if_zoom_account_not_found(self):
        self.zoom_account.delete()
        request = self.get_request(self.url, user=self.user)
        view = ZoomAccountUpdateView()
        view.request = request

        with self.assertRaises(Http404):
            view.get_object()

    @patch("app.views.messages.success")
    def test_view_should_update_zoom_account(self, mock_success):
        self.zoom_account.enable_drm = False
        self.zoom_account.user = self.user
        self.zoom_account.organization = self.organization
        self.zoom_account.save()
        folder = self.create_asset(type=Asset.Type.FOLDER)
        form_data = urlencode(
            {"enable_drm": "on", "import_destination": str(folder.uuid)}
        )

        request = self.get_request(
            self.url,
            user=self.user,
            method="POST",
            data=form_data,
            content_type="application/x-www-form-urlencoded",
        )
        response = ZoomAccountUpdateView.as_view()(request)

        self.zoom_account.refresh_from_db()
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse("integrations_settings"))
        self.assertTrue(self.zoom_account.enable_drm)
        self.assertEqual(self.zoom_account.import_destination, folder)

        mock_success.assert_called_once_with(
            request, "Zoom settings updated successfully"
        )
