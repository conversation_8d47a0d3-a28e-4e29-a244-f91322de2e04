import json
import uuid
from urllib.parse import urljoin

import requests
import sentry_sdk
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, HttpResponseBadRequest
from django.shortcuts import get_object_or_404, redirect
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.views.generic.edit import UpdateView
from requests.compat import urlencode

from app.domain.zoom import (
    ZoomError,
    ZoomOAuthError,
    fetch_zoom_tokens_using_authorization_code,
    fetch_zoom_user_info,
    revoke_zoom_account,
    store_zoom_account_in_db,
    store_zoom_recording_webhook_event,
)
from app.models.zoom import EventType, WebhookStatus, ZoomAccount
from app.forms.zoom import ZoomAccountForm
from app.tasks.zoom import Import<PERSON>oomRecordingTask
from app.utils.zoom import is_valid_zoom_signature


@method_decorator(login_required, name="dispatch")
class ZoomAuthorizeView(View):
    def get(self, request):
        redirect_uri = urljoin(settings.SITE_URL, reverse("zoom_oauth_callback"))
        params = {
            "response_type": "code",
            "client_id": settings.ZOOM_CLIENT_ID,
            "redirect_uri": redirect_uri,
            "state": self._generate_state_token(request),
        }

        authorize_url = f"https://zoom.us/oauth/authorize?{urlencode(params)}"
        return redirect(authorize_url)

    def _generate_state_token(self, request):
        token = str(uuid.uuid4())
        request.session["zoom_oauth_state"] = token
        return token


@method_decorator(login_required, name="dispatch")
class ZoomOAuthCallbackView(View):
    def get(self, request):
        try:
            self._validate_state(request)

            authorization_code = request.GET.get("code")
            if not authorization_code:
                raise ZoomOAuthError("No authorization code received")

            tokens = fetch_zoom_tokens_using_authorization_code(authorization_code)
            user_info = fetch_zoom_user_info(tokens["access_token"])
            store_zoom_account_in_db(request.user, user_info, tokens)

            messages.success(request, "Successfully connected to Zoom!")
            return redirect(reverse("integrations_settings") + "?zoom_connected=true")

        except ZoomOAuthError:
            messages.error(
                request, "Failed to authenticate with Zoom. Please try again."
            )

        except requests.exceptions.RequestException as e:
            sentry_sdk.capture_exception(e)
            messages.error(
                request,
                "Failed to connect to Zoom. Please check your internet connection and try again.",
            )

        except Exception as e:
            sentry_sdk.capture_exception(e)
            messages.error(request, "An unexpected error occurred. Please try again.")

        finally:
            request.session.pop("zoom_oauth_state", None)

        return redirect("integrations_settings")

    def _validate_state(self, request):
        state = request.GET.get("state")
        stored_state = request.session.get("zoom_oauth_state")
        if not state or not stored_state or state != stored_state:
            raise ZoomOAuthError("Invalid OAuth state")


@method_decorator(login_required, name="dispatch")
class ZoomDisconnectView(View):
    def post(self, request):
        zoom_account = get_object_or_404(
            ZoomAccount,
            organization=request.user.current_organization,
            user=request.user,
        )

        try:
            revoke_zoom_account(zoom_account)
            messages.success(request, "Successfully disconnected from Zoom.")

        except ZoomError as e:
            messages.error(request, str(e))
            return redirect("integrations_settings")

        except Exception as e:
            sentry_sdk.capture_exception(e)
            messages.error(request, "Failed to disconnect from Zoom. Please try again.")

        return redirect("integrations_settings")


@method_decorator(csrf_exempt, name="dispatch")
@method_decorator(require_http_methods(["POST"]), name="dispatch")
class ZoomRecordingWebhookView(View):
    def post(self, request):
        try:
            body = json.loads(request.body)
        except json.JSONDecodeError:
            return HttpResponseBadRequest("Invalid JSON")

        if not is_valid_zoom_signature(request):
            sentry_sdk.capture_message("Invalid Zoom signature", level="error")
            return HttpResponseBadRequest("Invalid signature")

        webhook_log = store_zoom_recording_webhook_event(body)
        if (
            webhook_log
            and webhook_log.event_type == EventType.RECORDING_COMPLETED
            and not webhook_log.status == WebhookStatus.SKIPPED
        ):
            host_id = (
                webhook_log.payload.get("payload", {}).get("object", {}).get("host_id")
            )
            ImportZoomRecordingTask.apply_async(
                kwargs={
                    "webhook_log_id": webhook_log.id,
                    "organization_uuid": webhook_log.organization.uuid,
                    "zoom_user_id": host_id,
                },
            )

        return HttpResponse("OK", status=200)


@method_decorator(login_required, name="dispatch")
class ZoomAccountUpdateView(UpdateView):
    model = ZoomAccount
    form_class = ZoomAccountForm
    template_name = "settings/includes/zoom_settings_modal.html"

    def get_object(self):
        return get_object_or_404(
            ZoomAccount,
            organization=self.request.user.current_organization,
            user=self.request.user,
        )

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs["user"] = self.request.user
        return kwargs

    def get_success_url(self):
        messages.success(self.request, "Zoom settings updated successfully")
        return reverse("integrations_settings")

    def form_invalid(self, form):
        messages.error(
            self.request, "Failed to update Zoom settings. Please try again."
        )
        return redirect("integrations_settings")
