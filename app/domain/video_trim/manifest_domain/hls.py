import m3u8

from app.domain.video_trim.exceptions import ManifestHandlingError
from app.utils.hls_trimmer import (
    HLSTrimmerError,
    build_variant_url,
    create_simple_master_playlist,
    generate_trim_id,
    trim_playlist_from_url,
)
from app.utils.m3u8 import get_m3u8_content
from app.utils.manifest_storage import ManifestStorageError, upload_manifest_content


class TrimContext:
    def __init__(self, video, start_time, end_time):
        self.video = video
        self.start_time = start_time
        self.end_time = end_time
        self.trim_id = generate_trim_id()
        self.base_path = f"transcoded/{video.asset.uuid}"
        self.master_url = f"{video.organization.cdn_url}{self.base_path}/video.m3u8"
        self.master_path = f"{self.base_path}/trimmed_{self.trim_id}.m3u8"


def trim_hls_manifests(video, start_time, end_time):
    trim_context = TrimContext(video, start_time, end_time)

    try:
        master_content = get_m3u8_content(trim_context.master_url)
        if not master_content:
            raise ManifestHandlingError("Could not fetch master playlist")

        master_playlist = m3u8.loads(master_content)

        if master_playlist.segments:
            return _trim_single_variant(trim_context)

        if not master_playlist.playlists:
            raise ManifestHandlingError(
                "Invalid HLS playlist: no variants or segments found"
            )

        _trim_variant_playlists(trim_context, master_playlist)
        upload_manifest_content(
            trim_context.video.organization,
            master_playlist.dumps(),
            trim_context.master_path,
        )

        return _build_response(trim_context.master_path, trim_context.trim_id)

    except (ManifestStorageError, HLSTrimmerError) as e:
        raise ManifestHandlingError(f"Failed to trim HLS manifests: {e}") from e
    except Exception as e:
        raise ManifestHandlingError(
            f"Unexpected error while trimming HLS manifests: {e}"
        ) from e


def _trim_variant_playlists(trim_context, master_playlist):
    for variant in master_playlist.playlists:
        if not getattr(variant, "uri", None):
            continue

        # Extract the actual path from the variant URI instead of assuming structure
        original_variant_path = variant.uri
        variant_url = build_variant_url(original_variant_path, trim_context.master_url)
        trimmed_content = trim_playlist_from_url(
            variant_url, trim_context.start_time, trim_context.end_time
        )

        # Determine the trimmed path based on the original variant path structure
        trimmed_path = _get_trimmed_variant_path(
            trim_context.base_path, original_variant_path, trim_context.trim_id
        )

        upload_manifest_content(
            trim_context.video.organization, trimmed_content, trimmed_path
        )

        # Update the variant URI to point to the trimmed manifest in the same directory structure
        variant.uri = _get_relative_trimmed_path(original_variant_path, trim_context.trim_id)


def _trim_single_variant(trim_context):
    # For single variant, the master URL itself is the variant playlist
    # We'll use a default directory structure since there's no master manifest with variants
    default_variant_path = "default_h264/video.m3u8"

    trimmed_content = trim_playlist_from_url(
        trim_context.master_url, trim_context.start_time, trim_context.end_time
    )

    # Use the same path generation logic for consistency
    trimmed_path = _get_trimmed_variant_path(
        trim_context.base_path, default_variant_path, trim_context.trim_id
    )

    upload_manifest_content(
        trim_context.video.organization, trimmed_content, trimmed_path
    )

    # Create master playlist pointing to the trimmed variant
    relative_trimmed_path = _get_relative_trimmed_path(default_variant_path, trim_context.trim_id)
    master_content = create_simple_master_playlist(relative_trimmed_path)
    upload_manifest_content(
        trim_context.video.organization, master_content, trim_context.master_path
    )

    return _build_response(trim_context.master_path, trim_context.trim_id)


def _get_trimmed_variant_path(base_path, original_variant_path, trim_id):
    """
    Generate the trimmed variant path based on the original variant path structure.

    Args:
        base_path: The base path for the video (e.g., "transcoded/asset_uuid")
        original_variant_path: The original variant URI from master manifest (e.g., "720p/video.m3u8")
        trim_id: The unique trim identifier

    Returns:
        The full path for the trimmed variant manifest
    """
    # Extract directory from original variant path
    if "/" in original_variant_path:
        variant_dir, _ = original_variant_path.rsplit("/", 1)
        # Create trimmed filename by adding trim_id prefix
        trimmed_filename = f"trimmed_{trim_id}.m3u8"
        return f"{base_path}/{variant_dir}/{trimmed_filename}"
    else:
        # If no directory structure, place in default directory
        return f"{base_path}/default_h264/trimmed_{trim_id}.m3u8"


def _get_relative_trimmed_path(original_variant_path, trim_id):
    """
    Generate the relative trimmed path for updating the variant URI in master manifest.

    Args:
        original_variant_path: The original variant URI from master manifest
        trim_id: The unique trim identifier

    Returns:
        The relative path for the trimmed variant manifest
    """
    # Extract directory from original variant path
    if "/" in original_variant_path:
        variant_dir, _ = original_variant_path.rsplit("/", 1)
        return f"{variant_dir}/trimmed_{trim_id}.m3u8"
    else:
        # If no directory structure, use default
        return f"default_h264/trimmed_{trim_id}.m3u8"


def _build_response(path, trim_id):
    return {
        "master": path,
        "trim_id": trim_id,
    }
