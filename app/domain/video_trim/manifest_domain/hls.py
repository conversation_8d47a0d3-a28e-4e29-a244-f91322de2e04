import m3u8

from app.domain.video_trim.exceptions import ManifestHandlingError
from app.utils.hls_trimmer import (
    HLSTrimmerError,
    build_variant_url,
    create_simple_master_playlist,
    determine_quality_folder_from_variant,
    generate_trim_id,
    trim_playlist_from_url,
)
from app.utils.m3u8 import get_m3u8_content
from app.utils.manifest_storage import ManifestStorageError, upload_manifest_content


class TrimContext:
    def __init__(self, video, start_time, end_time):
        self.video = video
        self.start_time = start_time
        self.end_time = end_time
        self.trim_id = generate_trim_id()
        self.base_path = f"transcoded/{video.asset.uuid}"
        self.master_url = f"{video.organization.cdn_url}{self.base_path}/video.m3u8"
        self.master_path = f"{self.base_path}/trimmed_{self.trim_id}.m3u8"


def trim_hls_manifests(video, start_time, end_time):
    trim_context = TrimContext(video, start_time, end_time)

    try:
        master_content = get_m3u8_content(trim_context.master_url)
        if not master_content:
            raise ManifestHandlingError("Could not fetch master playlist")

        master_playlist = m3u8.loads(master_content)

        if master_playlist.segments:
            return _trim_single_variant(trim_context)

        if not master_playlist.playlists:
            raise ManifestHandlingError(
                "Invalid HLS playlist: no variants or segments found"
            )

        _trim_variant_playlists(trim_context, master_playlist)
        upload_manifest_content(
            trim_context.video.organization,
            master_playlist.dumps(),
            trim_context.master_path,
        )

        return _build_response(trim_context.master_path, trim_context.trim_id)

    except (ManifestStorageError, HLSTrimmerError) as e:
        raise ManifestHandlingError(f"Failed to trim HLS manifests: {e}") from e
    except Exception as e:
        raise ManifestHandlingError(
            f"Unexpected error while trimming HLS manifests: {e}"
        ) from e


def _trim_variant_playlists(trim_context, master_playlist):
    for variant in master_playlist.playlists:
        if not getattr(variant, "uri", None):
            continue

        quality_folder = determine_quality_folder_from_variant(variant)
        variant_url = build_variant_url(variant.uri, trim_context.master_url)
        trimmed_content = trim_playlist_from_url(
            variant_url, trim_context.start_time, trim_context.end_time
        )
        trimmed_path = f"{trim_context.base_path}/{quality_folder}/trimmed_{trim_context.trim_id}.m3u8"

        upload_manifest_content(
            trim_context.video.organization, trimmed_content, trimmed_path
        )
        variant.uri = f"{quality_folder}/trimmed_{trim_context.trim_id}.m3u8"


def _trim_single_variant(trim_context):
    quality_folder = "default_h264"
    trimmed_content = trim_playlist_from_url(
        trim_context.master_url, trim_context.start_time, trim_context.end_time
    )
    trimmed_path = (
        f"{trim_context.base_path}/{quality_folder}/trimmed_{trim_context.trim_id}.m3u8"
    )

    upload_manifest_content(
        trim_context.video.organization, trimmed_content, trimmed_path
    )

    master_content = create_simple_master_playlist(
        f"{quality_folder}/trimmed_{trim_context.trim_id}.m3u8"
    )
    upload_manifest_content(
        trim_context.video.organization, master_content, trim_context.master_path
    )

    return _build_response(trim_context.master_path, trim_context.trim_id)


def _build_response(path, trim_id):
    return {
        "master": path,
        "trim_id": trim_id,
    }
